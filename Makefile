# Makefile cho Docker Swarm RTMP Stack

.PHONY: help init build deploy update remove status logs scale clean

STACK_NAME := rtmp-stack
COMPOSE_FILE := docker-compose.yml

# Default target
help: ## Hiển thị help
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-15s\033[0m %s\n", $$1, $$2}'

init: ## Khởi tạo Docker Swarm
	@echo "Khởi tạo Docker Swarm..."
	@docker swarm init || echo "Swarm đã được khởi tạo"

build: ## Build Docker image
	@echo "Building Docker image..."
	@docker build -f Dockerfile.txt -t rtmp:latest .

deploy: build ## Deploy stack lên Swarm
	@echo "Deploying stack $(STACK_NAME)..."
	@docker stack deploy -c $(COMPOSE_FILE) $(STACK_NAME)

update: build ## Update stack
	@echo "Updating stack $(STACK_NAME)..."
	@docker stack deploy -c $(COMPOSE_FILE) $(STACK_NAME)

remove: ## Xóa stack
	@echo "Removing stack $(STACK_NAME)..."
	@docker stack rm $(STACK_NAME)

status: ## Xem trạng thái stack
	@echo "=== STACK STATUS ==="
	@docker stack ls | grep $(STACK_NAME) || echo "Stack not found"
	@echo ""
	@echo "=== SERVICES ==="
	@docker stack services $(STACK_NAME) 2>/dev/null || echo "No services found"
	@echo ""
	@echo "=== TASKS ==="
	@docker stack ps $(STACK_NAME) 2>/dev/null || echo "No tasks found"

logs: ## Xem logs của service
	@echo "Showing logs for $(STACK_NAME)_nginx-rtmp..."
	@docker service logs -f $(STACK_NAME)_nginx-rtmp

scale-up: ## Scale service lên 3 replicas
	@echo "Scaling service to 3 replicas..."
	@docker service scale $(STACK_NAME)_nginx-rtmp=3

scale-down: ## Scale service xuống 1 replica
	@echo "Scaling service to 1 replica..."
	@docker service scale $(STACK_NAME)_nginx-rtmp=1

clean: remove ## Xóa stack và cleanup
	@echo "Cleaning up..."
	@docker system prune -f
	@docker volume prune -f

restart: remove deploy ## Restart stack (remove + deploy)
	@echo "Stack restarted"

# Development targets
dev-logs: ## Xem logs realtime
	@docker service logs -f --tail 100 $(STACK_NAME)_nginx-rtmp

dev-shell: ## Vào shell của container
	@CONTAINER_ID=$$(docker ps -q -f "label=com.docker.swarm.service.name=$(STACK_NAME)_nginx-rtmp" | head -1); \
	if [ -n "$$CONTAINER_ID" ]; then \
		docker exec -it $$CONTAINER_ID /bin/bash; \
	else \
		echo "No running container found"; \
	fi

dev-inspect: ## Inspect service
	@docker service inspect $(STACK_NAME)_nginx-rtmp

# Monitoring targets
monitor: ## Hiển thị thông tin monitoring
	@echo "=== NODE STATUS ==="
	@docker node ls
	@echo ""
	@echo "=== SERVICE STATUS ==="
	@docker service ls
	@echo ""
	@echo "=== NETWORK STATUS ==="
	@docker network ls | grep $(STACK_NAME)

health: ## Kiểm tra health của services
	@echo "Checking service health..."
	@docker service ps $(STACK_NAME)_nginx-rtmp --format "table {{.Name}}\t{{.CurrentState}}\t{{.Error}}"

# Backup targets
backup-config: ## Backup configuration files
	@echo "Backing up configuration..."
	@tar -czf backup-config-$$(date +%Y%m%d-%H%M%S).tar.gz nginx.conf docker-compose.yml Dockerfile.txt

backup-data: ## Backup HLS data
	@echo "Backing up HLS data..."
	@tar -czf backup-hls-$$(date +%Y%m%d-%H%M%S).tar.gz hls/
