
worker_processes auto;
rtmp_auto_push on;
events {}

rtmp {
    server {
        listen 1935;

        application live {
            live on;

            # Transcode để ép keyframe interval
            exec_push ffmpeg -i rtmp://localhost/live/$name -vcodec libx264 -vprofile baseline -preset ultrafast -tune zerolatency -g 30 -keyint_min 30 -s 1280x720 -an -f flv rtmp://localhost/transcoded/$name;

            # Allow publishing from anywhere
            allow publish all;

            # Enable recording if needed
            # record all;
            # record_path /tmp/recordings;
        }

        application transcoded {
            live on;

            # HLS cho stream đã transcode (Low Latency)
            hls on;
            hls_path /tmp/hls;
            hls_fragment 1s;
            hls_playlist_length 6s;
            hls_sync 10ms;
            hls_cleanup on;
            hls_fragment_slicing plain;
            hls_type live;
            hls_nested on;
            hls_continuous on;

            # Chỉ cho phép localhost push (từ transcode)
            allow publish 127.0.0.1;
            deny publish all;
        }
    }
}

http {
    sendfile on;
    tcp_nopush on;
    keepalive_timeout 65;

    server {
        listen 80;
        server_name _;

          location /hls {
            types {
                application/vnd.apple.mpegurl m3u8;
                video/mp2t ts;
            }
            root /tmp;
            tcp_nodelay on;
            gzip off;
        }
    }
}