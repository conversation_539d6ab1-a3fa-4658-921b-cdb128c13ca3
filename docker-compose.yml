version: '3.8'

services:
  nginx-rtmp:
    image: rtmp:latest  # Cần build image trư<PERSON><PERSON> khi deploy
    ports:
      - "1935:1935"
      - "8935:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./hls:/tmp/hls
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
      rollback_config:
        parallelism: 1
        delay: 10s
      placement:
        constraints:
          - node.role == manager  # Hoặc worker tùy theo yêu cầu
    networks:
      - rtmp-network

networks:
  rtmp-network:
    driver: overlay
    attachable: true