version: '3.8'

services:
  nginx-rtmp:
    build:
      context: .
      dockerfile: Dockerfile.txt
    ports:
      - "1935:1935"
      - "8935:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./hls:/tmp/hls
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
      rollback_config:
        parallelism: 1
        delay: 10s
    networks:
      - rtmp-network

networks:
  rtmp-network:
    driver: overlay
    attachable: true