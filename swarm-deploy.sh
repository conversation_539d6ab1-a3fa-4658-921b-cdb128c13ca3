#!/bin/bash

# Script để quản lý Docker Swarm stack cho RTMP server

STACK_NAME="rtmp-stack"
COMPOSE_FILE="docker-compose.yml"

# Màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Hàm hiển thị help
show_help() {
    echo "Sử dụng: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  init      - Khởi tạo Docker Swarm"
    echo "  build     - Build Docker image"
    echo "  deploy    - Deploy stack lên Swarm"
    echo "  update    - Update stack"
    echo "  remove    - Xóa stack"
    echo "  status    - Xem trạng thái stack"
    echo "  logs      - Xem logs của service"
    echo "  scale     - Scale service (ví dụ: $0 scale nginx-rtmp=3)"
    echo "  help      - Hiển thị help này"
}

# Kiểm tra Docker Swarm đã được khởi tạo chưa
check_swarm() {
    if ! docker info --format '{{.Swarm.LocalNodeState}}' | grep -q "active"; then
        echo -e "${RED}Docker Swarm chưa được khởi tạo!${NC}"
        echo "Chạy: $0 init để khởi tạo Swarm"
        exit 1
    fi
}

# Khởi tạo Docker Swarm
init_swarm() {
    echo -e "${YELLOW}Khởi tạo Docker Swarm...${NC}"
    docker swarm init
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Docker Swarm đã được khởi tạo thành công!${NC}"
    else
        echo -e "${RED}Lỗi khi khởi tạo Docker Swarm${NC}"
        exit 1
    fi
}

# Build Docker image
build_image() {
    echo -e "${YELLOW}Building Docker image...${NC}"
    docker build -f Dockerfile.txt -t rtmp:latest .
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Image đã được build thành công!${NC}"
    else
        echo -e "${RED}Lỗi khi build image${NC}"
        exit 1
    fi
}

# Deploy stack
deploy_stack() {
    check_swarm
    echo -e "${YELLOW}Deploying stack ${STACK_NAME}...${NC}"
    docker stack deploy -c ${COMPOSE_FILE} ${STACK_NAME}
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Stack đã được deploy thành công!${NC}"
        echo "Kiểm tra trạng thái: $0 status"
    else
        echo -e "${RED}Lỗi khi deploy stack${NC}"
        exit 1
    fi
}

# Update stack
update_stack() {
    check_swarm
    echo -e "${YELLOW}Updating stack ${STACK_NAME}...${NC}"
    docker stack deploy -c ${COMPOSE_FILE} ${STACK_NAME}
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Stack đã được update thành công!${NC}"
    else
        echo -e "${RED}Lỗi khi update stack${NC}"
        exit 1
    fi
}

# Remove stack
remove_stack() {
    check_swarm
    echo -e "${YELLOW}Removing stack ${STACK_NAME}...${NC}"
    docker stack rm ${STACK_NAME}
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Stack đã được xóa thành công!${NC}"
    else
        echo -e "${RED}Lỗi khi xóa stack${NC}"
        exit 1
    fi
}

# Xem trạng thái stack
show_status() {
    check_swarm
    echo -e "${YELLOW}Trạng thái stack ${STACK_NAME}:${NC}"
    echo ""
    echo "=== SERVICES ==="
    docker stack services ${STACK_NAME}
    echo ""
    echo "=== TASKS ==="
    docker stack ps ${STACK_NAME}
}

# Xem logs
show_logs() {
    check_swarm
    SERVICE_NAME="${STACK_NAME}_nginx-rtmp"
    echo -e "${YELLOW}Logs của service ${SERVICE_NAME}:${NC}"
    docker service logs -f ${SERVICE_NAME}
}

# Scale service
scale_service() {
    check_swarm
    if [ -z "$2" ]; then
        echo -e "${RED}Vui lòng cung cấp thông tin scale (ví dụ: nginx-rtmp=3)${NC}"
        exit 1
    fi
    
    SERVICE_NAME="${STACK_NAME}_$2"
    echo -e "${YELLOW}Scaling service ${SERVICE_NAME}...${NC}"
    docker service scale ${SERVICE_NAME}
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Service đã được scale thành công!${NC}"
    else
        echo -e "${RED}Lỗi khi scale service${NC}"
        exit 1
    fi
}

# Main script logic
case "$1" in
    init)
        init_swarm
        ;;
    build)
        build_image
        ;;
    deploy)
        deploy_stack
        ;;
    update)
        update_stack
        ;;
    remove)
        remove_stack
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    scale)
        scale_service "$@"
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}Command không hợp lệ: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
