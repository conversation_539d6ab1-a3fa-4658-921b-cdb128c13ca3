# 🎥 RTMP Streaming Server

> Hệ thống streaming server mạnh mẽ sử dụng Nginx RTMP module, hỗ trợ streaming thời gian thực và chuyển đổi sang HLS.

## ✨ Tính năng

- 📡 **RTMP Streaming** - Nhận stream từ OBS Studio, FFmpeg, và các phần mềm streaming khác
- 🌐 **HLS Output** - Tự động chuyển đổi RTMP thành HLS để phát trên web
- ⚡ **Low Latency** - Tối ưu hóa độ trễ thấp cho streaming thời gian thực
- 🔄 **Auto Transcoding** - Tự động transcode với FFmpeg tích hợp
- 🐳 **Docker Ready** - Triển khai dễ dàng với Docker Compose

## 🚀 Bắt đầu nhanh

### Yêu cầu hệ thống
- Docker & Docker Compose
- 2GB RAM (khuyến nghị 4GB+)
- 2 CPU cores (cho transcoding)

### Cài đặt và chạy

```bash
# 1. Clone repository
git clone https://github.com/stealer-smile/rtmp.git
cd rtmp

# 2. Khởi chạy server
docker-compose up -d

# 3. Kiểm tra trạng thái
docker-compose ps
```

Chỉ cần 3 bước đơn giản và server đã sẵn sàng! 🎉

## 📡 Cách sử dụng

### Streaming với OBS Studio

1. **Mở OBS Studio** và vào `Settings` → `Stream`
2. **Cấu hình stream:**
   - Service: `Custom`
   - Server: `rtmp://YOUR_SERVER_IP:1935/live`
   - Stream Key: `your_stream_name` (ví dụ: `demo`)
3. **Bắt đầu streaming** bằng nút `Start Streaming`

### Xem stream

**Trên VLC Media Player:**
```
http://YOUR_SERVER_IP:8935/hls/your_stream_name/index.m3u8
```

**Trên trình duyệt web:**
Sử dụng video.js hoặc hls.js để phát HLS stream.

### Streaming với FFmpeg

```bash
# Stream từ webcam
ffmpeg -f v4l2 -i /dev/video0 -c:v libx264 -preset ultrafast -f flv rtmp://YOUR_SERVER_IP:1935/live/demo

# Stream từ file video
ffmpeg -re -i video.mp4 -c:v libx264 -c:a aac -f flv rtmp://YOUR_SERVER_IP:1935/live/demo
```

## 🌐 Endpoints và Ports

| Service | Port | Endpoint | Mô tả |
|---------|------|----------|-------|
| RTMP | `1935` | `rtmp://server:1935/live/{key}` | Nhận stream input |
| HLS | `8935` | `http://server:8935/hls/{key}/index.m3u8` | HLS output |

## ⚙️ Cấu hình nâng cao

### Tùy chỉnh chất lượng stream

Chỉnh sửa file `nginx.conf`:

```nginx
# Thay đổi độ phân giải và bitrate
exec_push ffmpeg -i rtmp://localhost/live/$name
    -vcodec libx264 -vprofile baseline -preset ultrafast
    -s 1920x1080 -b:v 2000k -maxrate 2000k -bufsize 4000k
    -f flv rtmp://localhost/transcoded/$name;
```

### Bảo mật stream

```nginx
application live {
    live on;
    # Callback authentication
    on_publish http://your-auth-server.com/auth;
}
```

## 🔧 Quản lý và Monitoring

### Kiểm tra logs

```bash
# Xem logs realtime
docker-compose logs -f

# Logs của nginx
docker-compose exec nginx-rtmp tail -f /var/log/nginx/access.log
```

### Restart và update

```bash
# Restart server
docker-compose restart

# Update và rebuild
docker-compose down
docker-compose up -d --build
```

## 🌍 Production Setup

### Reverse Proxy với SSL

Tạo file `/etc/nginx/sites-available/rtmp`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location /hls {
        proxy_pass http://127.0.0.1:8935/hls;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;

        # CORS headers
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
    }
}
```

### Docker Compose cho Production

```yaml
version: '3.8'
services:
  nginx-rtmp:
    build: .
    restart: unless-stopped
    ports:
      - "1935:1935"
      - "127.0.0.1:8935:80"  # Chỉ bind localhost
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./hls:/tmp/hls
      - /var/log/nginx:/var/log/nginx
```

## 🛠️ Troubleshooting

### Stream không kết nối được

```bash
# Kiểm tra port
netstat -tulpn | grep :1935

# Test connection
telnet YOUR_SERVER_IP 1935
```

### HLS không tạo ra

```bash
# Kiểm tra thư mục HLS
ls -la hls/

# Kiểm tra permissions
docker-compose exec nginx-rtmp ls -la /tmp/hls/
```