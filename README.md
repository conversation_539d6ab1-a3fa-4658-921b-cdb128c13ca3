# 🎥 RTMP Streaming Server

> Hệ thống streaming server mạnh mẽ sử dụng Nginx RTMP module, hỗ trợ streaming thời gian thực và chuyển đổi sang HLS.

## ✨ Tính năng

- 📡 **RTMP Streaming** - Nhận stream từ OBS Studio, FFmpeg, và các phần mềm streaming khác
- 🌐 **HLS Output** - Tự động chuyển đổi RTMP thành HLS để phát trên web
- ⚡ **Low Latency** - Tối ưu hóa độ trễ thấp cho streaming thời gian thực
- 🔄 **Auto Transcoding** - Tự động transcode với FFmpeg tích hợp
- 🐳 **Docker Ready** - Triển khai dễ dàng với Docker Compose

## 🚀 Bắt đầu nhanh

### Yêu cầu hệ thống
- Docker & Docker Compose
- 2GB RAM (khuyến nghị 4GB+)
- 2 CPU cores (cho transcoding)

### Cài đặt và chạy

```bash
# 1. Clone repository
git clone https://github.com/stealer-smile/rtmp.git
cd rtmp

# 2. Khởi chạy server
docker-compose up -d

# 3. Kiểm tra trạng thái
docker-compose ps
```

Chỉ cần 3 bước đơn giản và server đã sẵn sàng! 🎉

## 📡 Cách sử dụng

### Streaming với OBS Studio

1. **Mở OBS Studio** và vào `Settings` → `Stream`
2. **Cấu hình stream:**
   - Service: `Custom`
   - Server: `rtmp://YOUR_SERVER_IP:1935/live`
   - Stream Key: `your_stream_name` (ví dụ: `demo`)
3. **Bắt đầu streaming** bằng nút `Start Streaming`

### Xem stream

**Trên VLC Media Player:**
```
http://YOUR_SERVER_IP:8935/hls/your_stream_name/index.m3u8
```

**Trên trình duyệt web:**
Sử dụng video.js hoặc hls.js để phát HLS stream.

### Streaming với FFmpeg

```bash
# Stream từ webcam
ffmpeg -f v4l2 -i /dev/video0 -c:v libx264 -preset ultrafast -f flv rtmp://YOUR_SERVER_IP:1935/live/demo

# Stream từ file video
ffmpeg -re -i video.mp4 -c:v libx264 -c:a aac -f flv rtmp://YOUR_SERVER_IP:1935/live/demo
```

## 🌐 Endpoints và Ports

| Service | Port | Endpoint | Mô tả |
|---------|------|----------|-------|
| RTMP | `1935` | `rtmp://server:1935/live/{key}` | Nhận stream input |
| HLS | `8935` | `http://server:8935/hls/{key}/index.m3u8` | HLS output |

## ⚙️ Cấu hình nâng cao

### Hiểu về FFmpeg Transcoding

Hệ thống sử dụng FFmpeg để transcode stream tự động. Đây là lệnh hiện tại:

```nginx
exec_push ffmpeg -i rtmp://localhost/live/$name
    -vcodec libx264 -vprofile baseline -preset ultrafast
    -tune zerolatency -g 30 -keyint_min 30
    -s 1920x1080 -an
    -f flv rtmp://localhost/transcoded/$name;
```

**Giải thích từng tham số:**

| Tham số | Ý nghĩa | Giá trị hiện tại |
|---------|---------|------------------|
| `-i rtmp://localhost/live/$name` | Input stream | Stream gốc từ application `live` |
| `-vcodec libx264` | Video codec | H.264 encoding |
| `-vprofile baseline` | H.264 profile | Baseline (tương thích cao) |
| `-preset ultrafast` | Encoding speed | Nhanh nhất (chất lượng thấp hơn) |
| `-tune zerolatency` | Tối ưu hóa | Độ trễ thấp cho live streaming |
| `-g 30` | GOP size | Keyframe mỗi 30 frames |
| `-keyint_min 30` | Min keyframe interval | Tối thiểu 30 frames |
| `-s 1920x1080` | Resolution | Full HD 1080p |
| `-an` | Audio | Loại bỏ audio (chỉ video) |
| `-f flv` | Output format | FLV format cho RTMP |

### Tùy chỉnh chất lượng stream

**1. Thay đổi độ phân giải:**
```nginx
# HD 720p
-s 1280x720

# 4K
-s 3840x2160

# Mobile-friendly
-s 854x480
```

**2. Cân bằng chất lượng vs tốc độ:**
```nginx
# Chất lượng cao hơn (chậm hơn)
-preset fast

# Cân bằng
-preset medium

# Chất lượng tốt nhất (rất chậm)
-preset veryslow
```

**3. Thêm bitrate control:**
```nginx
exec_push ffmpeg -i rtmp://localhost/live/$name
    -vcodec libx264 -vprofile baseline -preset ultrafast
    -tune zerolatency -g 30 -keyint_min 30
    -s 1920x1080 -b:v 2000k -maxrate 2500k -bufsize 5000k
    -an -f flv rtmp://localhost/transcoded/$name;
```

**4. Giữ lại audio:**
```nginx
# Thay -an bằng:
-c:a aac -b:a 128k
```

**5. Multiple quality streams:**
```nginx
application live {
    live on;

    # 1080p stream
    exec_push ffmpeg -i rtmp://localhost/live/$name
        -vcodec libx264 -preset ultrafast -s 1920x1080 -b:v 3000k
        -f flv rtmp://localhost/hd/$name;

    # 720p stream
    exec_push ffmpeg -i rtmp://localhost/live/$name
        -vcodec libx264 -preset ultrafast -s 1280x720 -b:v 1500k
        -f flv rtmp://localhost/sd/$name;
}
```

### Bảo mật stream

```nginx
application live {
    live on;
    # Callback authentication
    on_publish http://your-auth-server.com/auth;
}
```

## 🔧 Quản lý và Monitoring

### Kiểm tra logs

```bash
# Xem logs realtime
docker-compose logs -f

# Logs của nginx
docker-compose exec nginx-rtmp tail -f /var/log/nginx/access.log
```

### Restart và update

```bash
# Restart server
docker-compose restart

# Update và rebuild
docker-compose down
docker-compose up -d --build
```

## 🌍 Production Setup

### Reverse Proxy với SSL

Tạo file `/etc/nginx/sites-available/rtmp`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location /hls {
        proxy_pass http://127.0.0.1:8935/hls;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;

        # CORS headers
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
    }
}
```

### Docker Compose cho Production

```yaml
version: '3.8'
services:
  nginx-rtmp:
    build: .
    restart: unless-stopped
    ports:
      - "1935:1935"
      - "127.0.0.1:8935:80"  # Chỉ bind localhost
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./hls:/tmp/hls
      - /var/log/nginx:/var/log/nginx
```

## 🛠️ Troubleshooting

### Stream không kết nối được

```bash
# Kiểm tra port
netstat -tulpn | grep :1935

# Test connection
telnet YOUR_SERVER_IP 1935
```

### HLS không tạo ra

```bash
# Kiểm tra thư mục HLS
ls -la hls/

# Kiểm tra permissions
docker-compose exec nginx-rtmp ls -la /tmp/hls/
```

### Performance issues

```bash
# Monitor resource usage
docker stats

# Kiểm tra CPU usage của FFmpeg
docker-compose exec nginx-rtmp top
```

**Tối ưu performance:**

1. **Giảm chất lượng transcoding:**
```nginx
# Thay đổi preset (từ nhanh đến chậm)
-preset ultrafast  # Nhanh nhất, chất lượng thấp
-preset superfast
-preset veryfast   # Khuyến nghị cho live streaming
-preset faster
-preset fast
-preset medium     # Cân bằng
-preset slow       # Chất lượng cao
-preset veryslow   # Chất lượng tốt nhất, rất chậm
```

2. **Giảm độ phân giải:**
```nginx
# Từ 1920x1080 xuống 1280x720
-s 1280x720

# Hoặc 854x480 cho mobile
-s 854x480
```

3. **Tắt transcoding (nếu không cần):**
```nginx
application live {
    live on;

    # Bỏ dòng exec_push để tắt transcoding
    # exec_push ffmpeg ...

    # HLS trực tiếp từ stream gốc
    hls on;
    hls_path /tmp/hls;
}
```

4. **Hardware acceleration (nếu có GPU):**
```nginx
# NVIDIA GPU
exec_push ffmpeg -hwaccel nvdec -i rtmp://localhost/live/$name
    -c:v h264_nvenc -preset fast -s 1920x1080
    -f flv rtmp://localhost/transcoded/$name;

# Intel Quick Sync
exec_push ffmpeg -hwaccel qsv -i rtmp://localhost/live/$name
    -c:v h264_qsv -preset fast -s 1920x1080
    -f flv rtmp://localhost/transcoded/$name;
```

### FFmpeg errors

```bash
# Xem logs FFmpeg chi tiết
docker-compose logs nginx-rtmp | grep ffmpeg

# Test FFmpeg command thủ công
docker-compose exec nginx-rtmp ffmpeg -version
```

**Lỗi thường gặp:**

1. **"No such file or directory"**
   - Kiểm tra đường dẫn FFmpeg trong container
   - Đảm bảo FFmpeg đã được cài đặt

2. **"Connection refused"**
   - Kiểm tra application `transcoded` có tồn tại
   - Kiểm tra network giữa các application

3. **High CPU usage**
   - Giảm preset speed
   - Giảm độ phân giải
   - Tắt transcoding nếu không cần

## 📊 Monitoring

### Nginx RTMP Stats

Thêm vào nginx.conf:

```nginx
http {
    server {
        listen 8080;
        location /stat {
            rtmp_stat all;
            rtmp_stat_stylesheet stat.xsl;
        }
    }
}
```

Truy cập: `http://localhost:8080/stat`

### Logs

```bash
# Realtime logs
docker-compose logs -f

# Logs của nginx
docker exec rtmp tail -f /var/log/nginx/access.log
docker exec rtmp tail -f /var/log/nginx/error.log
```