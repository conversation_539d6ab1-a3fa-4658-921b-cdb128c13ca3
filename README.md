# RTMP Streaming Server với Docker

## 📺 Tổng quan

Hệ thống RTMP streaming server sử dụng Nginx với module RTMP, hỗ trợ:
- **RTMP Streaming**: Nhận stream từ OBS, FFmpeg, v.v.
- **HLS Output**: Chuyển đổi RTMP thành HLS cho web playback
- **Low Latency**: Tối ưu cho streaming thời gian thực
- **Transcoding**: Tự động transcode với FFmpeg
- **Web Interface**: Xem stream qua trình duyệt

## 🚀 Hướng dẫn sử dụng

### 1. Clone và khởi chạy

```bash
# Clone repository
git clone https://github.com/hungtx2001/rtmp.git
cd rtmp

# Khởi chạy hệ thống
docker-compose up -d

# Xem logs
docker-compose logs -f
```

### 2. Kiểm tra trạng thái

```bash
# Xem container đang chạy
docker-compose ps

# Xem logs realtime
docker-compose logs -f nginx-rtmp

# Kiểm tra resource usage
docker stats rtmp
```

### 3. Dừng hệ thống

```bash
# Dừng containers
docker-compose down

# Dừng và xóa volumes
docker-compose down -v
```

## 🌐 Ports và Endpoints

| Service | Port | Mô tả |
|---------|------|-------|
| **RTMP** | `1935` | Nhận stream từ OBS/FFmpeg |
| **HTTP** | `8935` | Web interface và HLS playback |

### URLs quan trọng:
- **RTMP Ingest**: `rtmp://localhost:1935/live/{stream_key}`
- **HLS Playback**: `http://localhost:8935/hls/{stream_key}/index.m3u8`
- **Web Player**: `http://localhost:8935/` (nếu có)

## 📺 Cách sử dụng streaming

### 1. Streaming với OBS Studio

1. Mở OBS Studio
2. Vào **Settings** → **Stream**
3. Chọn **Service**: Custom
4. **Server**: `rtmp://ip_server:1935/live`
5. **Stream Key**: `test` (hoặc tên bất kỳ)
6. Nhấn **Start Streaming**

### 2. Xem stream

```bash
# Với VLC
 http://ip_server:8935/hls/test/index.m3u8
```

### Các tham số quan trọng:
- **hls_fragment**: Độ dài mỗi segment (1s = low latency)
- **hls_playlist_length**: Số segments trong playlist
- **exec_push**: FFmpeg transcoding command

### Thêm authentication:

```nginx
application live {
    live on;

    # Chỉ cho phép publish từ localhost
    allow publish 127.0.0.1;
    deny publish all;

    # Hoặc dùng on_publish callback
    on_publish http://localhost/auth;
}
```

### SSL/HTTPS setup:

```
# /etc/nginx/conf.d/rtmp.yendinh.me.conf
server {
    listen 80;
    server_name rtmp.yendinh.me;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name rtmp.yendinh.me;

    ssl_certificate /etc/letsencrypt/live/rtmp.yendinh.me/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/rtmp.yendinh.me/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/rtmp.yendinh.me/chain.pem;


    # Proxy HLS từ container (map ra host port 8935)
    location /hls {
        proxy_pass http://127.0.0.1:8935/hls;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Connection "";

        chunked_transfer_encoding off;
        proxy_buffering off;
        proxy_cache off;

        add_header Access-Control-Allow-Origin *;
    }
}
```