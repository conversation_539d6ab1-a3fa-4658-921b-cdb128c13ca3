# Docker Swarm Deployment cho RTMP Server

## Tổng quan

Dự án này đã được chuyển đổi để sử dụng Docker Swarm mode, cho phép:
- High availability
- Load balancing
- Rolling updates
- Service scaling
- Fault tolerance

## Yêu cầu

- Docker Engine 17.06.0+
- Docker Swarm mode được kích hoạt

## Cấu trúc file

- `docker-compose.yml` - Stack definition cho Docker Swarm
- `swarm-deploy.sh` - Script quản lý stack
- `Dockerfile.txt` - Docker image definition
- `nginx.conf` - Nginx configuration

## Hướng dẫn sử dụng

### 1. Khởi tạo Docker Swarm (chỉ cần làm 1 lần)

```bash
chmod +x swarm-deploy.sh
./swarm-deploy.sh init
```

### 2. Build Docker image

```bash
./swarm-deploy.sh build
```

### 3. Deploy stack

```bash
./swarm-deploy.sh deploy
```

### 4. <PERSON><PERSON><PERSON> tra trạng thái

```bash
./swarm-deploy.sh status
```

### 5. Xem logs

```bash
./swarm-deploy.sh logs
```

### 6. Scale service (tăng số replicas)

```bash
./swarm-deploy.sh scale nginx-rtmp=3
```

### 7. Update stack (sau khi thay đổi config)

```bash
./swarm-deploy.sh update
```

### 8. Xóa stack

```bash
./swarm-deploy.sh remove
```

## Các thay đổi chính so với Docker Compose

### 1. Loại bỏ `container_name`
Docker Swarm tự động quản lý tên container.

### 2. Thay `build` bằng `image`
Cần build image trước khi deploy stack.

### 3. Thêm section `deploy`
- `replicas`: Số lượng container instances
- `restart_policy`: Chính sách restart
- `update_config`: Cấu hình rolling update
- `placement`: Ràng buộc về node placement

### 4. Thêm overlay network
Cho phép communication giữa các node trong cluster.

## Ports và Services

- **RTMP**: Port 1935 (cho streaming)
- **HTTP**: Port 8935 (cho web interface/HLS)

## Volumes

- `./nginx.conf` → `/etc/nginx/nginx.conf` (read-only)
- `./hls` → `/tmp/hls` (HLS output directory)

## Monitoring và Troubleshooting

### Kiểm tra services
```bash
docker service ls
docker service ps rtmp-stack_nginx-rtmp
```

### Kiểm tra logs
```bash
docker service logs rtmp-stack_nginx-rtmp
```

### Kiểm tra network
```bash
docker network ls
docker network inspect rtmp-stack_rtmp-network
```

### Inspect service
```bash
docker service inspect rtmp-stack_nginx-rtmp
```

## Rolling Updates

Khi cần update:
1. Thay đổi image tag trong `docker-compose.yml`
2. Chạy `./swarm-deploy.sh update`

Docker Swarm sẽ tự động:
- Update từng container một cách tuần tự
- Đảm bảo zero-downtime
- Rollback nếu có lỗi

## Multi-node Setup

Để thêm worker nodes:

```bash
# Trên manager node, lấy join token
docker swarm join-token worker

# Trên worker node, chạy command được hiển thị
docker swarm join --token <token> <manager-ip>:2377
```

## Security Notes

- Overlay network được mã hóa by default
- Chỉ manager nodes có thể deploy/update stacks
- Secrets có thể được sử dụng cho sensitive data

## Backup và Recovery

### Backup Swarm state
```bash
sudo tar -czf swarm-backup.tar.gz /var/lib/docker/swarm/
```

### Backup volumes
```bash
docker run --rm -v rtmp-stack_hls-data:/data -v $(pwd):/backup alpine tar czf /backup/hls-backup.tar.gz /data
```

## Troubleshooting

### Service không start
```bash
docker service ps rtmp-stack_nginx-rtmp --no-trunc
```

### Network issues
```bash
docker network inspect rtmp-stack_rtmp-network
```

### Node issues
```bash
docker node ls
docker node inspect <node-id>
```
