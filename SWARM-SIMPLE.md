# Docker Swarm - Hướng dẫn đơn giản

## Vấn đề với Docker Swarm và Build

**L<PERSON>u ý quan trọng**: Docker Swarm **KHÔNG** hỗ trợ build trực tiếp từ Dockerfile trong file docker-compose.yml khi sử dụng `docker stack deploy`.

## Gi<PERSON>i pháp đơn giản

### Cách 1: Build trước, deploy sau (Khuyến nghị)

```bash
# 1. Build image trước
docker build -f Dockerfile.txt -t rtmp:latest .

# 2. Khởi tạo swarm (chỉ cần 1 lần)
docker swarm init

# 3. Deploy stack
docker stack deploy -c docker-compose.yml rtmp-stack
```

### Cách 2: Sử dụng docker-compose thông thường (Không phải Swarm)

Nếu bạn muốn build trực tiếp từ Dockerfile, hãy sử dụng docker-compose bì<PERSON> thường:

```bash
# Tạo file docker-compose-dev.yml
version: '3.8'

services:
  nginx-rtmp:
    build:
      context: .
      dockerfile: Dockerfile.txt
    container_name: rtmp
    ports:
      - "1935:1935"
      - "8935:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./hls:/tmp/hls
    restart: unless-stopped

# Chạy với docker-compose
docker-compose -f docker-compose-dev.yml up -d
```

## Lệnh cơ bản cho Docker Swarm

```bash
# Khởi tạo swarm
docker swarm init

# Build image
docker build -f Dockerfile.txt -t rtmp:latest .

# Deploy stack
docker stack deploy -c docker-compose.yml rtmp-stack

# Xem trạng thái
docker stack services rtmp-stack
docker stack ps rtmp-stack

# Xem logs
docker service logs rtmp-stack_nginx-rtmp

# Scale service
docker service scale rtmp-stack_nginx-rtmp=3

# Update stack
docker stack deploy -c docker-compose.yml rtmp-stack

# Xóa stack
docker stack rm rtmp-stack
```

## Tại sao Docker Swarm không build được?

Docker Swarm được thiết kế để:
- Deploy các image đã build sẵn
- Quản lý production workloads
- Đảm bảo consistency across nodes

Build process thường chỉ cần làm 1 lần, không phải mỗi lần deploy.

## Kết luận

- **Development**: Dùng `docker-compose up` (có build)
- **Production**: Dùng `docker stack deploy` (chỉ deploy image có sẵn)
