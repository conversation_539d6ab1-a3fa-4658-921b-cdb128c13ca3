# 🎬 FFmpeg Transcoding Guide

## 📋 Tổng quan

Hệ thống RTMP sử dụng FFmpeg để transcode stream real-time, chuyển đổi từ stream gốc sang format tối ưu cho HLS.

## 🔧 Lệnh FFmpeg hiện tại

```bash
exec_push ffmpeg -i rtmp://localhost/live/$name 
    -vcodec libx264 -vprofile baseline -preset ultrafast 
    -tune zerolatency -g 30 -keyint_min 30 
    -s 1920x1080 -an 
    -f flv rtmp://localhost/transcoded/$name;
```

## 📖 Giải thích chi tiết từng tham số

### Input và Output
| Tham số | Mô tả | Giá trị |
|---------|-------|---------|
| `-i rtmp://localhost/live/$name` | Input stream | Stream từ application `live` |
| `-f flv` | Output format | FLV format cho RTMP |
| `rtmp://localhost/transcoded/$name` | Output destination | Gửi đến application `transcoded` |

### Video Encoding
| Tham số | Mô tả | Giá trị | Tác động |
|---------|-------|---------|----------|
| `-vcodec libx264` | Video codec | H.264 | Tương thích cao, chất lượng tốt |
| `-vprofile baseline` | H.264 profile | Baseline | Tương thích với mọi device |
| `-preset ultrafast` | Encoding speed | Ultrafast | Nhanh nhất, chất lượng thấp |
| `-tune zerolatency` | Optimization | Zero latency | Tối ưu cho live streaming |

### Keyframe Settings
| Tham số | Mô tả | Giá trị | Tác động |
|---------|-------|---------|----------|
| `-g 30` | GOP size | 30 frames | Keyframe mỗi 30 frames |
| `-keyint_min 30` | Min keyframe interval | 30 frames | Đảm bảo keyframe đều đặn |

### Resolution và Audio
| Tham số | Mô tả | Giá trị | Tác động |
|---------|-------|---------|----------|
| `-s 1920x1080` | Resolution | Full HD | Chất lượng cao, CPU cao |
| `-an` | Audio | No audio | Loại bỏ audio để giảm tải |

## ⚙️ Tùy chỉnh theo nhu cầu

### 1. Thay đổi chất lượng video

**Preset options (từ nhanh đến chậm):**
```nginx
-preset ultrafast   # CPU thấp, chất lượng thấp
-preset superfast   
-preset veryfast    # Khuyến nghị cho live streaming
-preset faster      
-preset fast        
-preset medium      # Cân bằng tốt
-preset slow        # Chất lượng cao
-preset veryslow    # Chất lượng tốt nhất, CPU cao
```

**Profile options:**
```nginx
-vprofile baseline  # Tương thích cao nhất
-vprofile main      # Cân bằng
-vprofile high      # Chất lượng tốt nhất
```

### 2. Điều chỉnh độ phân giải

```nginx
# 4K Ultra HD
-s 3840x2160

# Full HD
-s 1920x1080

# HD Ready
-s 1280x720

# SD
-s 854x480

# Mobile
-s 640x360
```

### 3. Thêm bitrate control

```nginx
exec_push ffmpeg -i rtmp://localhost/live/$name 
    -vcodec libx264 -vprofile baseline -preset veryfast
    -tune zerolatency -g 30 -keyint_min 30
    -s 1920x1080 
    -b:v 3000k -maxrate 3500k -bufsize 7000k
    -f flv rtmp://localhost/transcoded/$name;
```

**Bitrate recommendations:**
- **4K**: 15000-25000k
- **1080p**: 3000-6000k  
- **720p**: 1500-3000k
- **480p**: 500-1500k
- **360p**: 200-500k

### 4. Giữ lại audio

```nginx
# Thay -an bằng:
-c:a aac -b:a 128k

# Hoặc copy audio gốc:
-c:a copy
```

**Audio bitrate recommendations:**
- **High quality**: 192k
- **Standard**: 128k
- **Low**: 64k

### 5. Multiple quality streams

```nginx
application live {
    live on;
    
    # 1080p High Quality
    exec_push ffmpeg -i rtmp://localhost/live/$name 
        -vcodec libx264 -preset fast -s 1920x1080 -b:v 4000k -c:a aac -b:a 128k
        -f flv rtmp://localhost/hd/$name;
    
    # 720p Medium Quality
    exec_push ffmpeg -i rtmp://localhost/live/$name 
        -vcodec libx264 -preset fast -s 1280x720 -b:v 2000k -c:a aac -b:a 96k
        -f flv rtmp://localhost/md/$name;
    
    # 480p Low Quality
    exec_push ffmpeg -i rtmp://localhost/live/$name 
        -vcodec libx264 -preset fast -s 854x480 -b:v 800k -c:a aac -b:a 64k
        -f flv rtmp://localhost/sd/$name;
}
```

## 🚀 Hardware Acceleration

### NVIDIA GPU (NVENC)

```nginx
exec_push ffmpeg -hwaccel nvdec -i rtmp://localhost/live/$name 
    -c:v h264_nvenc -preset fast -profile:v baseline
    -s 1920x1080 -b:v 3000k -maxrate 3500k -bufsize 7000k
    -c:a aac -b:a 128k
    -f flv rtmp://localhost/transcoded/$name;
```

### Intel Quick Sync (QSV)

```nginx
exec_push ffmpeg -hwaccel qsv -i rtmp://localhost/live/$name 
    -c:v h264_qsv -preset fast -profile:v baseline
    -s 1920x1080 -b:v 3000k -maxrate 3500k -bufsize 7000k
    -c:a aac -b:a 128k
    -f flv rtmp://localhost/transcoded/$name;
```

### AMD GPU (AMF)

```nginx
exec_push ffmpeg -hwaccel d3d11va -i rtmp://localhost/live/$name 
    -c:v h264_amf -quality speed -profile baseline
    -s 1920x1080 -b:v 3000k -maxrate 3500k -bufsize 7000k
    -c:a aac -b:a 128k
    -f flv rtmp://localhost/transcoded/$name;
```

## 📊 Performance Optimization

### CPU Usage vs Quality

| Preset | CPU Usage | Quality | Use Case |
|--------|-----------|---------|----------|
| ultrafast | Rất thấp | Thấp | Testing, low-end hardware |
| superfast | Thấp | Thấp-Trung bình | Budget streaming |
| veryfast | Trung bình | Trung bình | **Khuyến nghị cho live** |
| faster | Trung bình-Cao | Trung bình-Cao | Good balance |
| fast | Cao | Cao | High quality streaming |
| medium | Rất cao | Rất cao | Archive quality |

### Memory Usage

```nginx
# Giảm buffer size để tiết kiệm RAM
-bufsize 2000k  # Thay vì 7000k

# Giảm thread count
-threads 2      # Thay vì auto
```

### Network Optimization

```nginx
# Adaptive bitrate
-b:v 2000k -minrate 1000k -maxrate 3000k -bufsize 6000k

# Keyframe interval cho HLS
-g 60 -keyint_min 60  # 2 giây với 30fps
```

## 🛠️ Troubleshooting

### Common Errors

**1. "No such file or directory"**
```bash
# Kiểm tra FFmpeg có trong container
docker-compose exec nginx-rtmp which ffmpeg
docker-compose exec nginx-rtmp ffmpeg -version
```

**2. "Connection refused"**
```bash
# Kiểm tra application transcoded
# Đảm bảo có application transcoded trong nginx.conf
```

**3. High CPU usage**
```bash
# Monitor CPU
docker stats

# Giảm preset hoặc resolution
-preset veryfast  # Thay vì ultrafast
-s 1280x720      # Thay vì 1920x1080
```

**4. Audio/Video sync issues**
```bash
# Thêm sync options
-vsync 1 -async 1
```

### Debug Commands

```bash
# Test FFmpeg command manually
docker-compose exec nginx-rtmp ffmpeg \
  -i rtmp://localhost/live/test \
  -vcodec libx264 -preset ultrafast \
  -s 1280x720 -t 10 \
  -f flv test_output.flv

# Check stream info
docker-compose exec nginx-rtmp ffprobe \
  rtmp://localhost/live/test
```

## 📈 Advanced Configurations

### Conditional Transcoding

```nginx
# Chỉ transcode nếu resolution > 720p
exec_push bash -c 'if [ $(ffprobe -v quiet -select_streams v:0 -show_entries stream=height -of csv=p=0 rtmp://localhost/live/$name) -gt 720 ]; then ffmpeg -i rtmp://localhost/live/$name -s 1280x720 -f flv rtmp://localhost/transcoded/$name; fi';
```

### Recording + Transcoding

```nginx
application live {
    live on;
    
    # Transcode for streaming
    exec_push ffmpeg -i rtmp://localhost/live/$name 
        -vcodec libx264 -preset veryfast -s 1280x720
        -f flv rtmp://localhost/transcoded/$name;
    
    # Record original quality
    exec_record ffmpeg -i rtmp://localhost/live/$name 
        -vcodec copy -acodec copy 
        /tmp/recordings/$name-$(date +%Y%m%d_%H%M%S).mp4;
}
```

### Webhook Integration

```nginx
# Notify when transcoding starts
exec_push bash -c 'curl -X POST http://your-webhook.com/transcode-start -d "stream=$name" && ffmpeg -i rtmp://localhost/live/$name -s 1280x720 -f flv rtmp://localhost/transcoded/$name';
```
